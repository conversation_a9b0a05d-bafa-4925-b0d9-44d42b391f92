
<template>
  <div class="dashboard-container">
    <!-- 库存告警提示 - 顶部固定位置 -->
    <div v-if="showStockAlert && !loading" class="stock-alert-top">
      <el-alert
        v-if="stockAlert.productStockLackNum > 0"
        title="产品库存告警"
        type="warning"
        :description="`有 ${stockAlert.productStockLackNum} 种产品库存不足，请及时补充库存`"
        show-icon
        :closable="false"
        class="stock-alert-item animate-fade-in-down clickable-alert"
        @click="goToProductManage"
      />
      <el-alert
        v-if="stockAlert.materialStockLackNum > 0"
        title="原料库存告警"
        type="warning"
        :description="`有 ${stockAlert.materialStockLackNum} 种原料库存不足，请及时补充库存`"
        show-icon
        :closable="false"
        class="stock-alert-item animate-fade-in-down clickable-alert"
        @click="goToMaterialList"
      />
    </div>

    <div class="dashboard-content">
      <h1 class="dashboard-title">仓库管理系统</h1>
      <p class="dashboard-subtitle">欢迎使用WMS仓库管理系统</p>

      <!-- 移动端扫码按钮 -->
      <!-- <div v-if="showScanButton" class="scan-buttons-container"> -->
      <div class="scan-buttons-container">
        <button
          class="scan-btn"
          @click="goToScan"
        >
          <div class="scan-btn-icon">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M3 7V5a2 2 0 0 1 2-2h2"/>
              <path d="M17 3h2a2 2 0 0 1 2 2v2"/>
              <path d="M21 17v2a2 2 0 0 1-2 2h-2"/>
              <path d="M7 21H5a2 2 0 0 1-2-2v-2"/>
              <rect x="9" y="9" width="6" height="6"/>
            </svg>
          </div>
          <span class="scan-btn-text">扫码出库/入库</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { isMobile } from '@/utils/device'
import { getStockAlert } from '@/api/dashboard'
import type { StockAlertData } from '@/type/dashboard.type'

const router = useRouter()
const showScanButton = ref(false)
const loading = ref(false)
const stockAlert = ref<StockAlertData>({
  productStockLackNum: 0,
  materialStockLackNum: 0
})

// 计算是否显示库存告警
const showStockAlert = computed(() => {
  return stockAlert.value.productStockLackNum > 0 || stockAlert.value.materialStockLackNum > 0
})

// 获取库存告警数据
const fetchStockAlert = async () => {
  try {
    loading.value = true
    const data = await getStockAlert()
    stockAlert.value = data
  } catch (error) {
    console.error('获取库存告警数据失败:', error)
    // 静默处理错误，不显示错误提示，避免影响用户体验
    // 保持默认值，不影响页面正常显示
    stockAlert.value = {
      productStockLackNum: 0,
      materialStockLackNum: 0
    }
  } finally {
    loading.value = false
  }
}

// 检测是否为移动端并获取库存告警数据
onMounted(async () => {
  showScanButton.value = isMobile()
  await fetchStockAlert()
})

// 跳转到扫码页面
const goToScan = () => {
  router.push('/qr-scanner')
}

// 跳转到产品管理页面
const goToProductManage = () => {
  router.push({
    path: '/product/manage',
    query: { extra: 'stockLack' }
  })
}

// 跳转到原料列表页面
const goToMaterialList = () => {
  router.push({
    path: '/material/materialList',
    query: { extra: 'stockLack' }
  })
}
</script>

<style lang="scss" scoped>
.dashboard-container {
  height: 100%;
  background: linear-gradient(135deg, #eff6ff 0%, #e0e7ff 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  position: relative;
}

.dashboard-content {
  text-align: center;
  max-width: 28rem;
  width: 100%;
}

.dashboard-title {
  font-size: 1.875rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.dashboard-subtitle {
  font-size: 1.125rem;
  color: #6b7280;
  margin-bottom: 2rem;
}

.scan-buttons-container {
  margin-top: 2rem;
  display: flex;
  justify-content: center;
}

.scan-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 1.25rem 2rem;
  border-radius: 1rem;
  font-weight: 600;
  color: white;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%);

  &:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    transform: translateY(-2px);
    background: linear-gradient(135deg, #5b21b6 0%, #7c3aed 50%, #db2777 100%);
  }

  &:active {
    transform: translateY(-1px) scale(0.98);
  }
}

.scan-btn-icon {
  width: 1.5rem;
  height: 1.5rem;

  svg {
    width: 100%;
    height: 100%;
  }
}

.scan-btn-text {
  font-size: 1.125rem;
}

/* 库存告警样式 - 顶部固定位置 */
.stock-alert-top {
  position: absolute;
  top: 1rem;
  left: 1rem;
  right: 1rem;
  z-index: 10;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  max-width: calc(100% - 2rem);
}

.stock-alert-item {
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(10px);
  text-align: left;

  :deep(.el-alert__title) {
    text-align: left;
  }

  :deep(.el-alert__description) {
    text-align: left;
  }
}

.clickable-alert {
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 12px -1px rgba(0, 0, 0, 0.15), 0 4px 8px -1px rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stock-alert-top {
    top: 0.5rem;
    left: 0.5rem;
    right: 0.5rem;
    gap: 0.5rem;
    max-width: calc(100% - 1rem);
  }

  .stock-alert-item {
    border-radius: 0.375rem;
  }
}
</style>
